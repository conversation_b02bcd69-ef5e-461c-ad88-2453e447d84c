Project PGS {
  database_type: "PostgreSQL"
}

/*** Users, Roles, Permissions ***/

Table users {
  id uuid [pk]
  username varchar(255) [unique]
  email varchar(255) [unique]
  password_hash varchar(255)
  google_2fa_secret varchar(64)
  is_active boolean
  created_at timestamp
  updated_at timestamp
}

Table roles {
  id uuid [pk]
  name varchar(100) [unique]
  created_at timestamp
}

Table permissions {
  id uuid [pk]
  name varchar(100)
  category varchar(50)
}

Table role_permissions {
  role_id uuid [ref: > roles.id]
  permission_id uuid [ref: > permissions.id]
  Note: "PK(role_id, permission_id)"
}

Table user_roles {
  user_id uuid [ref: > users.id]
  role_id uuid [ref: > roles.id]
  Note: "PK(user_id, role_id)"
}

/*** Bank Accounts & Robots ***/

Table bank_accounts {
  id uuid [pk]
  nickname varchar(100)
  account_number varchar(64) [unique]
  holder_name varchar(100)
  account_type varchar(20)
  bank_name varchar(100)
  in_transfer_limit decimal(18,2)
  out_transfer_limit decimal(18,2)
  balance_limit decimal(18,2)
  current_balance decimal(18,2)
  daily_in_used decimal(18,2)
  daily_out_used decimal(18,2)
  freezed_daily_in_used decimal(18,2)
  freezed_daily_out_used decimal(18,2)
  total_in decimal(18,2)
  total_out decimal(18,2)
  last_limit_reset date
  enable_in boolean
  enable_out boolean
  is_active boolean
  created_at timestamp
  updated_at timestamp
}

Table robot_status_periods {
  id uuid [pk]
  bank_account_id uuid [ref: > bank_accounts.id]
  status varchar(20)
  from_time timestamp
  to_time timestamp
  reported_by varchar(100)
  created_at timestamp
}

/*** Transactions ***/

Table bank_transactions {
  id uuid [pk]
  bank_account_id uuid [ref: > bank_accounts.id]
  direction varchar(10)
  amount decimal(18,2)
  transaction_time timestamp
  reference varchar(255)
  status varchar(20)
  matched_request_id uuid
  created_at timestamp
}

/*** Deposit & Withdrawal Requests ***/

Table deposit_requests {
  id uuid [pk]
  user_id uuid [ref: > users.id]
  currency varchar(10)
  merchant_code varchar(50)
  order_number varchar(100)
  extend_param jsonb
  amount decimal(18,2)
  status varchar(20)
  matched_txn_id uuid [ref: > bank_transactions.id]
  created_at timestamp
}

Table withdrawal_requests {
  txn_id uuid [ref: > bank_transactions.id]
  id uuid [pk]
  user_id uuid [ref: > users.id]
  currency varchar(10)
  merchant_code varchar(50)
  order_number varchar(100)
  extend_param jsonb
  bank_account_id uuid [ref: > bank_accounts.id]
  amount decimal(18,2)
  status varchar(20)
  instruction_sent boolean
  created_at timestamp
}

/*** Exceptions & Audit Logs ***/

Table exceptions {
  id uuid [pk]
  type varchar(30)
  related_id uuid
  description text
  resolved_by uuid [ref: > users.id]
  resolved_at timestamp
  created_at timestamp
}

Table audit_logs {
  id uuid [pk]
  user_id uuid [ref: > users.id]
  action varchar(255)
  target_type varchar(64)
  target_id uuid
  description text
  timestamp timestamp
}