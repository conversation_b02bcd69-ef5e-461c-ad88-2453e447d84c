# Payment Gateway System - Scheduled Jobs

This document describes the automated scheduled jobs implemented in the Payment Gateway System to improve banking operations and maintain data consistency.

## Overview

The system includes two main scheduled jobs that run automatically:

1. **Daily Limit Reset Scheduler** - Resets daily usage limits at midnight
2. **Request Expiration Scheduler** - Handles expired deposit and withdrawal requests

## Daily Limit Reset Scheduler

### Purpose
Automatically resets daily usage limits for all active bank accounts at midnight, ensuring accurate daily tracking without manual intervention.

### Configuration
- **Schedule**: Runs at midnight (00:00) every day using cron expression `0 0 0 * * *`
- **Class**: `com.pgs.service.scheduler.DailyLimitResetScheduler`
- **Transaction**: Each reset operation is wrapped in a transaction

### What it does
1. Retrieves all active bank accounts
2. Calls `resetDailyLimitsIfNeeded()` on each account
3. Resets the following fields to zero if the last reset date is not today:
   - `dailyInUsed`
   - `dailyOutUsed`
   - `freezedDailyInUsed`
   - `freezedDailyOutUsed`
4. Updates `lastLimitReset` to current date
5. Logs the operation for audit purposes

### Error Handling
- Individual account failures don't stop the entire process
- Errors are logged and reported through the audit system
- Failed operations are re-thrown to ensure proper Spring scheduler error handling

## Request Expiration Scheduler

### Purpose
Automatically processes expired deposit and withdrawal requests, updating their status and releasing reserved amounts.

### Configuration
- **Schedule**: Runs every minute using `@Scheduled(fixedRate = 60000)`
- **Class**: `com.pgs.service.scheduler.RequestExpirationScheduler`
- **Transaction**: Each processing cycle is wrapped in a transaction

### What it does

#### For Expired Deposit Requests
1. Finds all `PENDING` deposit requests where `expiresAt < now`
2. Changes status from `PENDING` to `UNPAID`
3. Releases frozen amounts using `releaseFrozenDepositAmount()`
4. Logs each expiration for audit purposes

#### For Expired Withdrawal Requests
1. Finds all `PENDING` withdrawal requests where `expiresAt < now`
2. Changes status from `PENDING` to `EXPIRED`
3. Releases frozen amounts using `releaseFrozenWithdrawalAmount()`
4. Restores the bank account balance that was deducted during request creation
5. Logs each expiration for audit purposes

### Error Handling
- Individual request failures don't stop processing of other requests
- Errors are logged with request details for debugging
- Failed operations are re-thrown to ensure proper Spring scheduler error handling

## Configuration

### Application Properties

```yaml
pgs:
  scheduler:
    pool-size: 5                                    # Thread pool size for schedulers
    thread-name-prefix: "PGS-Scheduler-"           # Thread naming prefix
    wait-for-tasks-to-complete-on-shutdown: true   # Wait for tasks on shutdown
    await-termination-seconds: 30                  # Shutdown timeout
```

### Environment Variables
- `SCHEDULER_POOL_SIZE`: Override thread pool size (default: 5)
- `SCHEDULER_THREAD_PREFIX`: Override thread name prefix
- `SCHEDULER_WAIT_ON_SHUTDOWN`: Wait for tasks on shutdown (default: true)
- `SCHEDULER_TERMINATION_TIMEOUT`: Shutdown timeout in seconds (default: 30)

## Monitoring and Logging

### Audit Logs
Both schedulers create audit log entries for:
- Successful operations with counts
- Individual request/account processing
- Error conditions

### Application Logs
- INFO level: Summary of operations and counts
- DEBUG level: Individual account/request processing details
- ERROR level: Failures and exceptions

### Log Examples

```
INFO  - Daily limit reset completed successfully. Reset 15 bank accounts
INFO  - Processed 3 expired requests: 2 deposits, 1 withdrawals
ERROR - Error processing expired deposit request: ORDER_123 (ID: uuid)
```

## Impact on Business Logic

### Removed Manual Reset Calls
The following manual reset calls have been removed since they're now handled automatically:
- `DepositService.createDepositRequest()` - No longer calls `resetDailyLimitsIfNeeded()`
- `WithdrawalService.createWithdrawalRequest()` - No longer calls `resetDailyLimitsIfNeeded()`
- `BankAccountService.getBankAccountById()` - No longer calls `resetDailyLimitsIfNeeded()`

### Improved Reliability
- Daily limits are reset consistently at midnight regardless of system activity
- Expired requests are processed automatically without manual intervention
- Frozen amounts are properly released, preventing permanent locks
- Bank account balances are correctly restored for expired withdrawals

## Testing

### Unit Tests
- `SchedulerIntegrationTest` provides comprehensive testing of both schedulers
- Tests verify correct status changes, amount releases, and balance restoration
- Uses test database with controlled data for reliable testing

### Manual Testing
Schedulers can be manually triggered for testing:
```java
@Autowired
private DailyLimitResetScheduler dailyLimitResetScheduler;

@Autowired 
private RequestExpirationScheduler requestExpirationScheduler;

// Manually trigger (useful for testing)
dailyLimitResetScheduler.resetDailyLimits();
requestExpirationScheduler.processExpiredRequests();
```

## Troubleshooting

### Common Issues

1. **Scheduler not running**: Verify `@EnableScheduling` is present on main application class
2. **Database locks**: Check for long-running transactions that might block scheduler operations
3. **Memory issues**: Monitor thread pool usage and adjust pool size if needed
4. **Timezone issues**: Ensure server timezone matches business requirements for midnight reset

### Monitoring Queries

```sql
-- Check recent scheduler audit logs
SELECT * FROM audit_logs 
WHERE action LIKE '%RESET%' OR action LIKE '%EXPIRED%' 
ORDER BY timestamp DESC LIMIT 10;

-- Check for accounts that might need manual reset
SELECT * FROM bank_accounts 
WHERE is_active = true 
AND last_limit_reset < CURRENT_DATE;

-- Check for requests that should have been processed
SELECT * FROM deposit_requests 
WHERE status = 'PENDING' 
AND expires_at < NOW();
```
