package com.pgs.service;

import com.pgs.dto.bankaccount.*;
import com.pgs.entity.BankAccount;
import com.pgs.exception.BadRequestException;
import com.pgs.exception.ResourceNotFoundException;
import com.pgs.repository.BankAccountRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class BankAccountService {

    private final BankAccountRepository bankAccountRepository;
    private final AuditService auditService;

    @Transactional
    public BankAccountResponse createBankAccount(CreateBankAccountRequest request) {
        // todo: different bank might have the same number, so we should check for both bank name and account number
        // Check if account number already exists
        if (bankAccountRepository.existsByAccountNumber(request.getAccountNumber())) {
            throw new BadRequestException("Account number already exists");
        }

        // Create bank account
        BankAccount bankAccount = BankAccount.builder()
            .nickname(request.getNickname())
            .accountNumber(request.getAccountNumber())
            .bankName(request.getBankName())
            .inTransferLimit(request.getInTransferLimit())
            .outTransferLimit(request.getOutTransferLimit())
            .balanceLimit(request.getBalanceLimit())
            .holderName(request.getAccountHolderName())
            .accountType(request.getAccountType())
            .lastLimitReset(LocalDate.now())
            .isActive(true)
            .build();

        BankAccount savedAccount = bankAccountRepository.save(bankAccount);

        // todo: need to log who has done this action
        auditService.logUserAction(null, "BANK_ACCOUNT_CREATED", "BANK_ACCOUNT", savedAccount.getId(), 
            "Bank account created: " + savedAccount.getNickname());

        return mapToBankAccountResponse(savedAccount);
    }

    @Transactional(readOnly = true)
    public Page<BankAccountResponse> getAllBankAccounts(Pageable pageable) {
        return bankAccountRepository.findAllActive(pageable)
            .map(this::mapToBankAccountResponse);
    }

    @Transactional(readOnly = true)
    public BankAccountResponse getBankAccountById(UUID accountId) {
        BankAccount account = bankAccountRepository.findById(accountId)
            .orElseThrow(() -> new ResourceNotFoundException("Bank Account", "id", accountId));
        
        // Reset daily limits if needed
        account.resetDailyLimitsIfNeeded();
        
        return mapToBankAccountResponse(account);
    }

    @Transactional
    public BankAccountResponse updateBankAccount(UUID accountId, UpdateBankAccountRequest request) {
        BankAccount account = bankAccountRepository.findById(accountId)
            .orElseThrow(() -> new ResourceNotFoundException("Bank Account", "id", accountId));

        // Update fields if provided
        if (request.getNickname() != null) {
            account.setNickname(request.getNickname());
        }
        if (request.getInTransferLimit() != null) {
            account.setInTransferLimit(request.getInTransferLimit());
        }
        if (request.getOutTransferLimit() != null) {
            account.setOutTransferLimit(request.getOutTransferLimit());
        }
        if (request.getBalanceLimit() != null) {
            account.setBalanceLimit(request.getBalanceLimit());
        }
        if (request.getAccountHolderName() != null) {
            account.setHolderName(request.getAccountHolderName());
        }
        if (request.getAccountType() != null) {
            account.setAccountType(request.getAccountType());
        }
        if (request.getEnableIn() != null) {
            account.setEnableIn(request.getEnableIn());
        }
        if (request.getEnableOut() != null) {
            account.setEnableOut(request.getEnableOut());
        }

        BankAccount savedAccount = bankAccountRepository.save(account);

        // todo: need to log who has done this action
        auditService.logUserAction(null, "BANK_ACCOUNT_UPDATED", "BANK_ACCOUNT", savedAccount.getId(), 
            "Bank account updated: " + savedAccount.getNickname());

        return mapToBankAccountResponse(savedAccount);
    }

    @Transactional
    public void deleteBankAccount(UUID accountId) {
        BankAccount account = bankAccountRepository.findById(accountId)
            .orElseThrow(() -> new ResourceNotFoundException("Bank Account", "id", accountId));

        // Soft delete by setting isActive to false
        account.setIsActive(false);
        bankAccountRepository.save(account);

        // todo: need to log who has done this action
        auditService.logUserAction(null, "BANK_ACCOUNT_DELETED", "BANK_ACCOUNT", accountId, 
            "Bank account deleted: " + account.getNickname());
    }

    private BankAccountResponse mapToBankAccountResponse(BankAccount account) {
        return BankAccountResponse.builder()
            .accountId(account.getId())
            .nickname(account.getNickname())
            .accountNumber(account.getAccountNumber())
            .bankName(account.getBankName())
            .inTransferLimit(account.getInTransferLimit())
            .outTransferLimit(account.getOutTransferLimit())
            .balanceLimit(account.getBalanceLimit())
            .accountHolderName(account.getHolderName())
            .accountType(account.getAccountType())
            .currentBalance(account.getCurrentBalance())
            .dailyInUsed(account.getDailyInUsed())
            .dailyOutUsed(account.getDailyOutUsed())
            .freezedDailyInUsed(account.getFreezedDailyInUsed())
            .freezedDailyOutUsed(account.getFreezedDailyOutUsed())
            .totalIn(account.getTotalIn())
            .totalOut(account.getTotalOut())
            .availableInLimit(account.getAvailableInLimit())
            .availableOutLimit(account.getAvailableOutLimit())
            .enableIn(account.getEnableIn())
            .enableOut(account.getEnableOut())
            .isActive(account.getIsActive())
            .createdAt(account.getCreatedAt())
            .updatedAt(account.getUpdatedAt())
            .build();
    }
}
