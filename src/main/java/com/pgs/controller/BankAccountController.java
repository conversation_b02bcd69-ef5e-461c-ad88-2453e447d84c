package com.pgs.controller;

import com.pgs.dto.bankaccount.*;
import com.pgs.service.BankAccountService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("/frontend/bank-accounts")
@RequiredArgsConstructor
public class BankAccountController {

    private final BankAccountService bankAccountService;

    @PostMapping
    @PreAuthorize("hasAuthority('BANK_ACCOUNT_CREATE')")
    public ResponseEntity<BankAccountResponse> createBankAccount(@Valid @RequestBody CreateBankAccountRequest request) {
        BankAccountResponse response = bankAccountService.createBankAccount(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @GetMapping
    @PreAuthorize("hasAuthority('BANK_ACCOUNT_READ')")
    public ResponseEntity<Page<BankAccountResponse>> getAllBankAccounts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
            Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        
        Pageable pageable = PageRequest.of(page, size, sort);
        Page<BankAccountResponse> accounts = bankAccountService.getAllBankAccounts(pageable);
        
        return ResponseEntity.ok(accounts);
    }

    @GetMapping("/{accountId}")
    @PreAuthorize("hasAuthority('BANK_ACCOUNT_READ')")
    public ResponseEntity<BankAccountResponse> getBankAccountById(@PathVariable UUID accountId) {
        BankAccountResponse response = bankAccountService.getBankAccountById(accountId);
        return ResponseEntity.ok(response);
    }

    @PutMapping("/{accountId}")
    @PreAuthorize("hasAuthority('BANK_ACCOUNT_UPDATE')")
    public ResponseEntity<BankAccountResponse> updateBankAccount(
            @PathVariable UUID accountId,
            @Valid @RequestBody UpdateBankAccountRequest request) {
        
        BankAccountResponse response = bankAccountService.updateBankAccount(accountId, request);
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{accountId}")
    @PreAuthorize("hasAuthority('BANK_ACCOUNT_DELETE')")
    public ResponseEntity<Void> deleteBankAccount(@PathVariable UUID accountId) {
        bankAccountService.deleteBankAccount(accountId);
        return ResponseEntity.noContent().build();
    }

    //todo: add endpoints to enable_in and enable_out
}
