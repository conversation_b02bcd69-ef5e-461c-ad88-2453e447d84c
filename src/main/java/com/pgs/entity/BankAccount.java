package com.pgs.entity;

import com.pgs.enums.AccountType;
import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "bank_accounts")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BankAccount extends BaseEntity {

    //todo: add two boolean fields enable_in and enable_out

    @Column(nullable = false, length = 100)
    private String nickname;

    @Column(name = "account_number", unique = true, nullable = false, length = 64)
    private String accountNumber;

    @Column(name = "holder_name", nullable = false, length = 100)
    private String holderName;

    @Enumerated(EnumType.STRING)
    @Column(name = "account_type", nullable = false, length = 20)
    private AccountType accountType;

    @Column(name = "bank_name", nullable = false, length = 100)
    private String bankName;

    @Column(name = "in_transfer_limit", nullable = false, precision = 18, scale = 2)
    @Builder.Default
    private BigDecimal inTransferLimit = BigDecimal.ZERO;

    @Column(name = "out_transfer_limit", nullable = false, precision = 18, scale = 2)
    @Builder.Default
    private BigDecimal outTransferLimit = BigDecimal.ZERO;

    @Column(name = "balance_limit", nullable = false, precision = 18, scale = 2)
    @Builder.Default
    private BigDecimal balanceLimit = BigDecimal.ZERO;

    @Column(name = "current_balance", nullable = false, precision = 18, scale = 2)
    @Builder.Default
    private BigDecimal currentBalance = BigDecimal.ZERO;

    @Column(name = "daily_in_used", nullable = false, precision = 18, scale = 2)
    @Builder.Default
    private BigDecimal dailyInUsed = BigDecimal.ZERO;

    @Column(name = "daily_out_used", nullable = false, precision = 18, scale = 2)
    @Builder.Default
    private BigDecimal dailyOutUsed = BigDecimal.ZERO;

    //todo: add column freezed_daily_in_used and freezed_daily_out_used

    //todo: add column total_in and total_out

    @Column(name = "last_limit_reset")
    @Builder.Default
    private LocalDate lastLimitReset = LocalDate.now();

    @Column(name = "is_active")
    @Builder.Default
    private Boolean isActive = true;

    @OneToMany(mappedBy = "bankAccount", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private Set<BankTransaction> transactions = new HashSet<>();

    @OneToMany(mappedBy = "bankAccount", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private Set<RobotStatusPeriod> robotStatusPeriods = new HashSet<>();

    public BigDecimal getAvailableInLimit() {
        // todo: consider freezed_daily_in_used as well
        return inTransferLimit.subtract(dailyInUsed);
    }

    public BigDecimal getAvailableOutLimit() {
        // todo: consider freezed_daily_out_used
        return outTransferLimit.subtract(dailyOutUsed);
    }

    public boolean canAcceptDeposit(BigDecimal amount) {
        return isActive && 
               getAvailableInLimit().compareTo(amount) >= 0 &&
               currentBalance.add(amount).compareTo(balanceLimit) <= 0;
    }

    public boolean canProcessWithdrawal(BigDecimal amount) {
        return isActive && 
               getAvailableOutLimit().compareTo(amount) >= 0 &&
               currentBalance.compareTo(amount) >= 0;
    }

    public void resetDailyLimitsIfNeeded() {
        LocalDate today = LocalDate.now();
        if (!today.equals(lastLimitReset)) {
            dailyInUsed = BigDecimal.ZERO;
            dailyOutUsed = BigDecimal.ZERO;
            lastLimitReset = today;
        }
    }
}
