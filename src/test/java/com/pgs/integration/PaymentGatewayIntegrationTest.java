package com.pgs.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pgs.dto.deposit.DepositRequestDto;
import com.pgs.dto.withdrawal.WithdrawalRequestDto;
import com.pgs.entity.BankAccount;
import com.pgs.entity.User;
import com.pgs.enums.AccountType;
import com.pgs.repository.BankAccountRepository;
import com.pgs.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureTestMvc
@ActiveProfiles("test")
@Transactional
public class PaymentGatewayIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private BankAccountRepository bankAccountRepository;

    private User testUser;
    private BankAccount testBankAccount;

    @BeforeEach
    void setUp() {
        // Create test user
        testUser = User.builder()
            .username("testuser")
            .email("<EMAIL>")
            .password("password")
            .isActive(true)
            .build();
        testUser = userRepository.save(testUser);

        // Create test bank account
        testBankAccount = BankAccount.builder()
            .nickname("Test Bank Account")
            .accountNumber("**********")
            .bankName("Test Bank")
            .inTransferLimit(new BigDecimal("100000"))
            .outTransferLimit(new BigDecimal("50000"))
            .balanceLimit(new BigDecimal("1000000"))
            .holderName("Test Holder")
            .accountType(AccountType.SAVINGS)
            .currentBalance(new BigDecimal("50000"))
            .dailyInUsed(BigDecimal.ZERO)
            .dailyOutUsed(BigDecimal.ZERO)
            .freezedDailyInUsed(BigDecimal.ZERO)
            .freezedDailyOutUsed(BigDecimal.ZERO)
            .totalIn(BigDecimal.ZERO)
            .totalOut(BigDecimal.ZERO)
            .enableIn(true)
            .enableOut(true)
            .lastLimitReset(LocalDate.now())
            .isActive(true)
            .build();
        testBankAccount = bankAccountRepository.save(testBankAccount);
    }

    @Test
    void testDepositRequestCreation() throws Exception {
        DepositRequestDto request = new DepositRequestDto();
        request.setUserId(testUser.getId().toString());
        request.setAmount(new BigDecimal("1000"));
        request.setCurrency("THB");
        request.setMerchantCode("TEST_MERCHANT");
        request.setOrderNumber("ORDER_" + System.currentTimeMillis());

        mockMvc.perform(post("/deposit-request")
                .header("X-API-Key", "test-api-key")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.orderNumber").value(request.getOrderNumber()))
                .andExpect(jsonPath("$.amount").value(1000))
                .andExpect(jsonPath("$.status").value("PENDING"));
    }

    @Test
    void testWithdrawalRequestCreation() throws Exception {
        WithdrawalRequestDto request = new WithdrawalRequestDto();
        request.setUserId(testUser.getId().toString());
        request.setAmount(new BigDecimal("500"));
        request.setCurrency("THB");
        request.setMerchantCode("TEST_MERCHANT");
        request.setOrderNumber("WITHDRAWAL_" + System.currentTimeMillis());
        
        WithdrawalRequestDto.TargetAccount targetAccount = new WithdrawalRequestDto.TargetAccount();
        targetAccount.setAccountNumber("**********");
        targetAccount.setBankName("Target Bank");
        targetAccount.setAccountHolderName("Target Holder");
        request.setTargetAccount(targetAccount);

        mockMvc.perform(post("/withdrawal-request")
                .header("X-API-Key", "test-api-key")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.orderNumber").value(request.getOrderNumber()))
                .andExpect(jsonPath("$.amount").value(500))
                .andExpect(jsonPath("$.status").value("PENDING"));
    }

    @Test
    @WithMockUser(authorities = {"BANK_ACCOUNT_READ"})
    void testBankAccountManagement() throws Exception {
        mockMvc.perform(get("/frontend/bank-accounts"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray());
    }

    @Test
    @WithMockUser(authorities = {"ROLE_READ"})
    void testRoleManagement() throws Exception {
        mockMvc.perform(get("/frontend/roles"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray());
    }

    @Test
    @WithMockUser(authorities = {"EXCEPTION_READ"})
    void testExceptionHandling() throws Exception {
        mockMvc.perform(get("/frontend/exceptions/unpaid-requests"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray());

        mockMvc.perform(get("/frontend/exceptions/unmatched-transactions"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray());
    }

    @Test
    @WithMockUser(authorities = {"REPORT_READ"})
    void testReporting() throws Exception {
        mockMvc.perform(get("/frontend/reports/transactions"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray());

        mockMvc.perform(get("/frontend/reports/bank-balances"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray());
    }

    @Test
    @WithMockUser(authorities = {"AUDIT_READ"})
    void testAuditLogs() throws Exception {
        mockMvc.perform(get("/audit/logs"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray());
    }
}
