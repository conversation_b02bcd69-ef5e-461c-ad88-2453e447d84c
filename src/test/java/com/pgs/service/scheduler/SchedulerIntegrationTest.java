package com.pgs.service.scheduler;

import com.pgs.entity.BankAccount;
import com.pgs.entity.DepositRequest;
import com.pgs.entity.WithdrawalRequest;
import com.pgs.enums.AccountType;
import com.pgs.enums.DepositStatus;
import com.pgs.enums.WithdrawalStatus;
import com.pgs.repository.BankAccountRepository;
import com.pgs.repository.DepositRequestRepository;
import com.pgs.repository.WithdrawalRequestRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class SchedulerIntegrationTest {

    @Autowired
    private DailyLimitResetScheduler dailyLimitResetScheduler;

    @Autowired
    private RequestExpirationScheduler requestExpirationScheduler;

    @Autowired
    private BankAccountRepository bankAccountRepository;

    @Autowired
    private DepositRequestRepository depositRequestRepository;

    @Autowired
    private WithdrawalRequestRepository withdrawalRequestRepository;

    private BankAccount testBankAccount;

    @BeforeEach
    void setUp() {
        // Create test bank account with some daily usage
        testBankAccount = BankAccount.builder()
            .nickname("Test Account")
            .accountNumber("TEST123456")
            .bankName("Test Bank")
            .holderName("Test Holder")
            .accountType(AccountType.PERSONAL)
            .inTransferLimit(new BigDecimal("10000"))
            .outTransferLimit(new BigDecimal("5000"))
            .balanceLimit(new BigDecimal("100000"))
            .currentBalance(new BigDecimal("50000"))
            .dailyInUsed(new BigDecimal("1000"))
            .dailyOutUsed(new BigDecimal("500"))
            .freezedDailyInUsed(new BigDecimal("200"))
            .freezedDailyOutUsed(new BigDecimal("100"))
            .totalIn(new BigDecimal("25000"))
            .totalOut(new BigDecimal("15000"))
            .enableIn(true)
            .enableOut(true)
            .lastLimitReset(LocalDate.now().minusDays(1)) // Yesterday, so reset is needed
            .isActive(true)
            .build();
        testBankAccount = bankAccountRepository.save(testBankAccount);
    }

    @Test
    void testDailyLimitResetScheduler() {
        // Verify initial state
        assertEquals(new BigDecimal("1000"), testBankAccount.getDailyInUsed());
        assertEquals(new BigDecimal("500"), testBankAccount.getDailyOutUsed());
        assertEquals(new BigDecimal("200"), testBankAccount.getFreezedDailyInUsed());
        assertEquals(new BigDecimal("100"), testBankAccount.getFreezedDailyOutUsed());

        // Run the scheduler
        dailyLimitResetScheduler.resetDailyLimits();

        // Reload the account from database
        testBankAccount = bankAccountRepository.findById(testBankAccount.getId()).orElseThrow();

        // Verify reset occurred
        assertEquals(BigDecimal.ZERO, testBankAccount.getDailyInUsed());
        assertEquals(BigDecimal.ZERO, testBankAccount.getDailyOutUsed());
        assertEquals(BigDecimal.ZERO, testBankAccount.getFreezedDailyInUsed());
        assertEquals(BigDecimal.ZERO, testBankAccount.getFreezedDailyOutUsed());
        assertEquals(LocalDate.now(), testBankAccount.getLastLimitReset());

        // Verify totals are unchanged
        assertEquals(new BigDecimal("25000"), testBankAccount.getTotalIn());
        assertEquals(new BigDecimal("15000"), testBankAccount.getTotalOut());
    }

    @Test
    void testRequestExpirationScheduler_ExpiredDepositRequest() {
        // Create an expired deposit request
        DepositRequest expiredRequest = DepositRequest.builder()
            .orderNumber("EXPIRED_DEPOSIT_001")
            .amount(new BigDecimal("1000"))
            .currency("THB")
            .merchantCode("TEST")
            .status(DepositStatus.PENDING)
            .assignedBankAccount(testBankAccount)
            .expiresAt(LocalDateTime.now().minusMinutes(5)) // Expired 5 minutes ago
            .build();
        expiredRequest = depositRequestRepository.save(expiredRequest);

        // Set some frozen amount
        testBankAccount.setFreezedDailyInUsed(new BigDecimal("1000"));
        bankAccountRepository.save(testBankAccount);

        // Run the scheduler
        requestExpirationScheduler.processExpiredRequests();

        // Reload entities
        expiredRequest = depositRequestRepository.findById(expiredRequest.getId()).orElseThrow();
        testBankAccount = bankAccountRepository.findById(testBankAccount.getId()).orElseThrow();

        // Verify request status changed
        assertEquals(DepositStatus.UNPAID, expiredRequest.getStatus());

        // Verify frozen amount was released
        assertEquals(BigDecimal.ZERO, testBankAccount.getFreezedDailyInUsed());
    }

    @Test
    void testRequestExpirationScheduler_ExpiredWithdrawalRequest() {
        // Create an expired withdrawal request
        WithdrawalRequest expiredRequest = WithdrawalRequest.builder()
            .orderNumber("EXPIRED_WITHDRAWAL_001")
            .amount(new BigDecimal("500"))
            .currency("THB")
            .merchantCode("TEST")
            .bankAccount(testBankAccount)
            .targetAccountNumber("TARGET123")
            .targetBankName("Target Bank")
            .status(WithdrawalStatus.PENDING)
            .expiresAt(LocalDateTime.now().minusMinutes(5)) // Expired 5 minutes ago
            .build();
        expiredRequest = withdrawalRequestRepository.save(expiredRequest);

        // Set some frozen amount and reduce balance (simulating withdrawal request creation)
        BigDecimal originalBalance = testBankAccount.getCurrentBalance();
        testBankAccount.setFreezedDailyOutUsed(new BigDecimal("500"));
        testBankAccount.setCurrentBalance(originalBalance.subtract(new BigDecimal("500")));
        bankAccountRepository.save(testBankAccount);

        // Run the scheduler
        requestExpirationScheduler.processExpiredRequests();

        // Reload entities
        expiredRequest = withdrawalRequestRepository.findById(expiredRequest.getId()).orElseThrow();
        testBankAccount = bankAccountRepository.findById(testBankAccount.getId()).orElseThrow();

        // Verify request status changed
        assertEquals(WithdrawalStatus.EXPIRED, expiredRequest.getStatus());

        // Verify frozen amount was released and balance restored
        assertEquals(BigDecimal.ZERO, testBankAccount.getFreezedDailyOutUsed());
        assertEquals(originalBalance, testBankAccount.getCurrentBalance());
    }
}
